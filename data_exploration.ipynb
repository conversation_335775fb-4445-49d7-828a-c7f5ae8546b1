!pip install --upgrade jupyter ipywidgets

!pip install tensorflow

!pip install kagglehub

!pip install matplotlib

!pip install opencv-python

import kagglehub
import os # Import the os library to interact with the file system

# 1. Download the dataset and get the path
# It will be downloaded to a cache folder like C:\Users\<USER>\.Kaggle\datasets\...
path_to_dataset = kagglehub.dataset_download("leoscode/wound-segmentation-images")

print(f"Dataset downloaded to: {path_to_dataset}")

# 2. Use the returned path to see the contents
print("\nContents of the dataset folder:")
# os.listdir() lists all files and folders in a given path
file_list = os.listdir(path_to_dataset)
for f in file_list:
    print(f)

import os
import cv2
import matplotlib.pyplot as plt
import numpy as np

# This is the path we got from the kagglehub download
# Note: You might need to add '\\data_wound_seg' at the end if the files are in a subfolder
path_to_dataset = r'C:\Users\<USER>\.cache\kagglehub\datasets\leoscode\wound-segmentation-images\versions\1'

# Let's define the paths to the specific image and mask folders
train_images_path = os.path.join(path_to_dataset, 'data_wound_seg', 'train_images')
train_masks_path = os.path.join(path_to_dataset, 'data_wound_seg', 'train_masks')

# 1. Get a list of all image filenames
all_image_files = os.listdir(train_images_path)
sample_image_file = all_image_files[0] # Let's just pick the first one

# 2. Construct the full paths for the image and its corresponding mask
full_image_path = os.path.join(train_images_path, sample_image_file)
# The mask usually has the same name as the image
full_mask_path = os.path.join(train_masks_path, sample_image_file) 

print(f"Loading image: {full_image_path}")
print(f"Loading mask: {full_mask_path}")

# 3. Load the image and the mask
image = cv2.imread(full_image_path)
image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB) # Convert to RGB for display

# Masks are usually grayscale, so we load it in grayscale mode
mask = cv2.imread(full_mask_path, cv2.IMREAD_GRAYSCALE) 

# 4. Display them to verify
plt.figure(figsize=(10, 5))

plt.subplot(1, 2, 1)
plt.title("Wound Image")
plt.imshow(image)

plt.subplot(1, 2, 2)
plt.title("Wound Mask")
plt.imshow(mask, cmap='gray') # Use a gray colormap for the mask

plt.show()

import os
import kagglehub

# Get the path to the dataset from the Kaggle cache
path_to_dataset = kagglehub.dataset_download("leoscode/wound-segmentation-images")

# Define the paths to the image and mask folders
base_path = os.path.join(str(path_to_dataset), 'data_wound_seg')
train_images_dir = os.path.join(base_path, 'train_images')
train_masks_dir = os.path.join(base_path, 'train_masks')

# Get a sorted list of all image and mask filenames
train_image_files = sorted(os.listdir(train_images_dir))
train_mask_files = sorted(os.listdir(train_masks_dir))

# Verify that the number of images matches the number of masks
print(f"Found {len(train_image_files)} training images.")
print(f"Found {len(train_mask_files)} training masks.")

# Print the first 5 file names to ensure they match
print("\nFirst 5 image files:", train_image_files[:5])
print("First 5 mask files: ", train_mask_files[:5])

# It's a good practice to restart the kernel after a big installation
from IPython.display import display, HTML
display(HTML("<script>Jupyter.notebook.kernel.restart()</script>"))

import os
import tensorflow as tf

# --- Configuration ---
IMG_HEIGHT = 224
IMG_WIDTH = 224
BATCH_SIZE = 16

# --- Get the full file paths ---
# We assume train_images_dir and train_masks_dir are already defined from the previous cell
full_train_image_paths = [os.path.join(train_images_dir, f) for f in train_image_files]
full_train_mask_paths = [os.path.join(train_masks_dir, f) for f in train_mask_files]

def parse_image_and_mask(image_path, mask_path):
    """Loads and preprocesses a single image and mask."""
    # Read the image file
    image = tf.io.read_file(image_path)
    image = tf.image.decode_png(image, channels=3)
    image = tf.image.convert_image_dtype(image, tf.float32)
    image = tf.image.resize(image, [IMG_HEIGHT, IMG_WIDTH])

    # Read the mask file
    mask = tf.io.read_file(mask_path)
    mask = tf.image.decode_png(mask, channels=1)
    mask = tf.image.resize(mask, [IMG_HEIGHT, IMG_WIDTH])
    # Normalize mask to be 0 or 1
    mask = tf.cast(mask > 0.5, dtype=tf.float32)

    return image, mask

# Create the TensorFlow Dataset object
dataset = tf.data.Dataset.from_tensor_slices((full_train_image_paths, full_train_mask_paths))
dataset = dataset.map(parse_image_and_mask, num_parallel_calls=tf.data.AUTOTUNE)

# Configure the dataset for performance
dataset = dataset.shuffle(buffer_size=1000).batch(BATCH_SIZE).prefetch(buffer_size=tf.data.AUTOTUNE)

print("TensorFlow data pipeline created successfully!")
print(dataset)

from tensorflow.keras import layers, models

def build_unet_model(input_shape=(224, 224, 3)):
    """Builds a simplified U-Net model."""
    
    # All of these lines need to be indented
    inputs = layers.Input(input_shape)

    # --- Encoder (Downsampling Path) ---
    c1 = layers.Conv2D(16, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(inputs)
    c1 = layers.Dropout(0.1)(c1)
    c1 = layers.Conv2D(16, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(c1)
    p1 = layers.MaxPooling2D((2, 2))(c1)

    c2 = layers.Conv2D(32, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(p1)
    c2 = layers.Dropout(0.1)(c2)
    c2 = layers.Conv2D(32, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(c2)
    p2 = layers.MaxPooling2D((2, 2))(c2)

    # --- Bottleneck ---
    b = layers.Conv2D(64, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(p2)
    b = layers.Dropout(0.2)(b)
    b = layers.Conv2D(64, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(b)

    # --- Decoder (Upsampling Path) ---
    u2 = layers.Conv2DTranspose(32, (2, 2), strides=(2, 2), padding='same')(b)
    u2 = layers.concatenate([u2, c2])
    c7 = layers.Conv2D(32, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(u2)
    c7 = layers.Dropout(0.1)(c7)
    c7 = layers.Conv2D(32, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(c7)

    u3 = layers.Conv2DTranspose(16, (2, 2), strides=(2, 2), padding='same')(c7)
    u3 = layers.concatenate([u3, c1])
    c8 = layers.Conv2D(16, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(u3)
    c8 = layers.Dropout(0.1)(c8)
    c8 = layers.Conv2D(16, (3, 3), activation='relu', kernel_initializer='he_normal', padding='same')(c8)

    # --- Output Layer ---
    outputs = layers.Conv2D(1, (1, 1), activation='sigmoid')(c8)

    model = models.Model(inputs=[inputs], outputs=[outputs])
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    
    return model

# These lines should NOT be indented because they are outside the function
model = build_unet_model()
model.summary()

# --- Configuration ---
EPOCHS = 10  # Let's start with 10 epochs for our first run

# --- Train the model ---
# The model.fit() command starts the training process.
# We pass it our dataset and tell it how many epochs to run.
history = model.fit(
    dataset,
    epochs=EPOCHS
)

print("Model training complete!")

# Save the trained model for later use
model.save('wound_segmentation_model_v1.keras')
print("Model saved successfully as wound_segmentation_model_v1.keras")

import matplotlib.pyplot as plt

# --- Plot the training history ---
plt.figure(figsize=(12, 5))

# Plot training accuracy
plt.subplot(1, 2, 1)
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.title('Training Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()

# Plot training loss
plt.subplot(1, 2, 2)
plt.plot(history.history['loss'], label='Training Loss', color='red')
plt.title('Training Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

plt.tight_layout()
plt.show()

import tensorflow as tf
import os
import matplotlib.pyplot as plt
import numpy as np

# --- Load the saved model ---
model = tf.keras.models.load_model('wound_segmentation_model_v1.keras')
print("Model loaded successfully!")

# --- Create a data pipeline for the TEST set ---
test_images_dir = os.path.join(base_path, 'test_images')
test_masks_dir = os.path.join(base_path, 'test_masks')

test_image_files = sorted(os.listdir(test_images_dir))
test_mask_files = sorted(os.listdir(test_masks_dir))

full_test_image_paths = [os.path.join(test_images_dir, f) for f in test_image_files]
full_test_mask_paths = [os.path.join(test_masks_dir, f) for f in test_mask_files]

# We don't need to shuffle the test set
test_dataset = tf.data.Dataset.from_tensor_slices((full_test_image_paths, full_test_mask_paths))
test_dataset = test_dataset.map(parse_image_and_mask) # We can reuse our parsing function
test_dataset = test_dataset.batch(BATCH_SIZE)

# --- Make predictions on a batch of test images ---
images, true_masks = next(iter(test_dataset)) # Get one batch
predicted_masks = model.predict(images)

# The model outputs probabilities, so we round them to get a binary mask (0 or 1)
predicted_masks = tf.round(predicted_masks)

# --- Display the results ---
num_to_display = 4  # Let's display the first 4 images from the batch

plt.figure(figsize=(15, 15))
for i in range(num_to_display):
    # Display original image
    plt.subplot(num_to_display, 3, i * 3 + 1)
    plt.title("Original Image")
    plt.imshow(images[i])
    plt.axis('off')

    # Display true mask
    plt.subplot(num_to_display, 3, i * 3 + 2)
    plt.title("True Mask")
    plt.imshow(true_masks[i], cmap='gray')
    plt.axis('off')

    # Display predicted mask
    plt.subplot(num_to_display, 3, i * 3 + 3)
    plt.title("Predicted Mask")
    plt.imshow(predicted_masks[i], cmap='gray')
    plt.axis('off')

plt.tight_layout()
plt.show()

import tensorflow as tf
from tensorflow.keras import layers

# --- 1. Define the Augmentation Layers ---
# This creates a small neural network that only performs random transformations.
data_augmentation = tf.keras.Sequential([
  layers.RandomFlip("horizontal_and_vertical"),
  layers.RandomRotation(0.2),
  layers.RandomBrightness(factor=0.2),
  layers.RandomContrast(factor=0.2),
], name="data_augmentation")


# --- 2. Create a new function to apply augmentation ---
# It's crucial that we apply the exact same random flip and rotation 
# to both the image and its mask so they still line up perfectly.
@tf.function
def augment_data(image, mask):
    # Stack the image and mask so we can transform them together
    stacked_image_mask = tf.concat([image, mask], axis=-1)
    
    # Apply the same random transformation to the stacked tensor
    augmented_stacked = data_augmentation(stacked_image_mask)
    
    # Unstack them back into a separate image and mask
    augmented_image = augmented_stacked[..., :3]
    augmented_mask = augmented_stacked[..., 3:]
    
    return augmented_image, augmented_mask


# --- 3. Rebuild the data pipeline with the augmentation step ---
# We start from our original dataset of file paths
dataset_for_augmentation = tf.data.Dataset.from_tensor_slices((full_train_image_paths, full_train_mask_paths))

# Now, our map function first parses the image, then augments it
dataset_for_augmentation = dataset_for_augmentation.map(parse_image_and_mask, num_parallel_calls=tf.data.AUTOTUNE)
dataset_for_augmentation = dataset_for_augmentation.map(augment_data, num_parallel_calls=tf.data.AUTOTUNE)

# Configure the dataset for performance as before
augmented_dataset = dataset_for_augmentation.shuffle(buffer_size=1000).batch(BATCH_SIZE).prefetch(buffer_size=tf.data.AUTOTUNE)

print("Augmented TensorFlow data pipeline created successfully!")
print(augmented_dataset)