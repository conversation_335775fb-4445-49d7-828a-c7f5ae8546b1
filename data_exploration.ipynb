!pip install --upgrade jupyter ipywidgets

!pip install tensorflow

!pip install kagglehub

!pip install matplotlib

!pip install opencv-python

import kagglehub
import os # Import the os library to interact with the file system

# 1. Download the dataset and get the path
# It will be downloaded to a cache folder like C:\Users\<USER>\.Kaggle\datasets\...
path_to_dataset = kagglehub.dataset_download("leoscode/wound-segmentation-images")

print(f"Dataset downloaded to: {path_to_dataset}")

# 2. Use the returned path to see the contents
print("\nContents of the dataset folder:")
# os.listdir() lists all files and folders in a given path
file_list = os.listdir(path_to_dataset)
for f in file_list:
    print(f)

import os
import cv2
import matplotlib.pyplot as plt
import numpy as np

# This is the path we got from the kagglehub download
# Note: You might need to add '\\data_wound_seg' at the end if the files are in a subfolder
path_to_dataset = r'C:\Users\<USER>\.cache\kagglehub\datasets\leoscode\wound-segmentation-images\versions\1'

# Let's define the paths to the specific image and mask folders
train_images_path = os.path.join(path_to_dataset, 'data_wound_seg', 'train_images')
train_masks_path = os.path.join(path_to_dataset, 'data_wound_seg', 'train_masks')

# 1. Get a list of all image filenames
all_image_files = os.listdir(train_images_path)
sample_image_file = all_image_files[0] # Let's just pick the first one

# 2. Construct the full paths for the image and its corresponding mask
full_image_path = os.path.join(train_images_path, sample_image_file)
# The mask usually has the same name as the image
full_mask_path = os.path.join(train_masks_path, sample_image_file) 

print(f"Loading image: {full_image_path}")
print(f"Loading mask: {full_mask_path}")

# 3. Load the image and the mask
image = cv2.imread(full_image_path)
image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB) # Convert to RGB for display

# Masks are usually grayscale, so we load it in grayscale mode
mask = cv2.imread(full_mask_path, cv2.IMREAD_GRAYSCALE) 

# 4. Display them to verify
plt.figure(figsize=(10, 5))

plt.subplot(1, 2, 1)
plt.title("Wound Image")
plt.imshow(image)

plt.subplot(1, 2, 2)
plt.title("Wound Mask")
plt.imshow(mask, cmap='gray') # Use a gray colormap for the mask

plt.show()

import os
import kagglehub

# Get the path to the dataset from the Kaggle cache
path_to_dataset = kagglehub.dataset_download("leoscode/wound-segmentation-images")

# Define the paths to the image and mask folders
base_path = os.path.join(str(path_to_dataset), 'data_wound_seg')
train_images_dir = os.path.join(base_path, 'train_images')
train_masks_dir = os.path.join(base_path, 'train_masks')

# Get a sorted list of all image and mask filenames
train_image_files = sorted(os.listdir(train_images_dir))
train_mask_files = sorted(os.listdir(train_masks_dir))

# Verify that the number of images matches the number of masks
print(f"Found {len(train_image_files)} training images.")
print(f"Found {len(train_mask_files)} training masks.")

# Print the first 5 file names to ensure they match
print("\nFirst 5 image files:", train_image_files[:5])
print("First 5 mask files: ", train_mask_files[:5])

import tensorflow as tf

# --- Configuration ---
IMG_HEIGHT = 224
IMG_WIDTH = 224
BATCH_SIZE = 16 # We will process images in batches of 16

# --- Get the full file paths ---
full_train_image_paths = [os.path.join(train_images_dir, f) for f in train_image_files]
full_train_mask_paths = [os.path.join(train_masks_dir, f) for f in train_mask_files]

def parse_image_and_mask(image_path, mask_path):
    """Loads and preprocesses a single image and mask."""
    # Read the image file
    image = tf.io.read_file(image_path)
    image = tf.image.decode_png(image, channels=3)
    image = tf.image.convert_image_dtype(image, tf.float32)
    image = tf.image.resize(image, [IMG_HEIGHT, IMG_WIDTH])

    # Read the mask file
    mask = tf.io.read_file(mask_path)
    mask = tf.image.decode_png(mask, channels=1)
    mask = tf.image.resize(mask, [IMG_HEIGHT, IMG_WIDTH])
    # Normalize mask to be 0 or 1
    mask = tf.cast(mask > 0.5, dtype=tf.float32)

    return image, mask

# Create the TensorFlow Dataset object
dataset = tf.data.Dataset.from_tensor_slices((full_train_image_paths, full_train_mask_paths))

# Use the 'map' function to apply our parsing function to each file path pair
dataset = dataset.map(parse_image_and_mask, num_parallel_calls=tf.data.AUTOTUNE)

# Configure the dataset for performance
dataset = dataset.shuffle(buffer_size=1000).batch(BATCH_SIZE).prefetch(buffer_size=tf.data.AUTOTUNE)

print("TensorFlow data pipeline created successfully!")
print(dataset)