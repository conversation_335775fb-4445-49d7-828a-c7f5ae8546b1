import tensorflow as tf
import numpy as np
from flask import Flask, request, jsonify
from PIL import Image
import io
import base64

# --- Configuration ---
IMG_HEIGHT = 224
IMG_WIDTH = 224

# --- Initialize Flask App ---
app = Flask(__name__)

# --- Load the Trained Model ---
# We use the v1 model because it's ready now.
try:
    model = tf.keras.models.load_model('wound_segmentation_model_v1.keras')
    print("Model loaded successfully!")
except Exception as e:
    print(f"Error loading model: {e}")
    model = None

# --- Define the Prediction Endpoint ---
@app.route('/predict', methods=) # <-- THIS LINE IS NOW FIXED
def predict():
    if model is None:
        return jsonify({'error': 'Model is not loaded!'}), 500

    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    try:
        # 1. Preprocess the image
        image = Image.open(file.stream).convert('RGB')
        image = image.resize((IMG_WIDTH, IMG_HEIGHT))
        image_array = np.array(image) / 255.0
        image_batch = np.expand_dims(image_array, axis=0)

        # 2. Make a prediction
        predicted_mask = model.predict(image_batch)
        predicted_mask = (predicted_mask > 0.5).astype(np.uint8) * 255 # Threshold and scale to 0-255

        # 3. Convert the predicted mask back to an image and send it
        mask_image = Image.fromarray(predicted_mask.squeeze(), mode='L')
        buffered = io.BytesIO()
        mask_image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')

        return jsonify({'mask': img_str})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# --- Run the App ---
if __name__ == '__main__':
    # Make sure to use a different port than the frontend, e.g., 5000
    app.run(host='0.0.0.0', port=5000)